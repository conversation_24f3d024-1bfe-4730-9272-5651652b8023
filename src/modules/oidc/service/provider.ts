import { App, Config, ILogger, Init, Inject, Provide, InjectClient } from '@midwayjs/core';
import { BaseService } from '@cool-midway/core';
import Provider, {
  AdapterFactory,
  Configuration as OIDCConfiguration,
  KoaContextWithOIDC,
} from 'oidc-provider';
import { Application } from '@midwayjs/koa';
import { OIDCStoreService } from './store';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { OidcClientEntity } from '../entity/client';
import { BaseSysUserEntity } from '../../base/entity/sys/user';
import { BaseSysRoleEntity } from '../../base/entity/sys/role';
import { BaseSysDepartmentEntity } from '../../base/entity/sys/department';
import { BaseSysUserRoleEntity } from '../../base/entity/sys/user_role';
import { CachingFactory, MidwayCache } from '@midwayjs/cache-manager';
import * as jwt from 'jsonwebtoken';
import * as mount from 'koa-mount';

@Provide()
export class OIDCProviderService extends BaseService {
  @App()
  app: Application;

  private provider: Provider;
  private issuer: string;

  @Config('module.base')
  jwtConfig;

  @Inject()
  store: OIDCStoreService;

  @InjectClient(CachingFactory, 'default')
  midwayCache: MidwayCache;

  @InjectEntityModel(OidcClientEntity)
  clientRepo: Repository<OidcClientEntity>;

  @InjectEntityModel(BaseSysUserEntity)
  baseSysUserEntity: Repository<BaseSysUserEntity>;

  @InjectEntityModel(BaseSysRoleEntity)
  baseSysRoleEntity: Repository<BaseSysRoleEntity>;

  @InjectEntityModel(BaseSysDepartmentEntity)
  baseSysDepartmentEntity: Repository<BaseSysDepartmentEntity>;

  @InjectEntityModel(BaseSysUserRoleEntity)
  baseSysUserRoleEntity: Repository<BaseSysUserRoleEntity>;

  @Inject()
  logger: ILogger;

  async findClientById(clientId: string) {
    try {
      this.logger.info(`Looking for OIDC client: ${clientId}`);
      const client = await this.clientRepo.findOneBy({ clientId, status: 1 });
      if (client) {
        this.logger.info(`Found OIDC client: ${clientId}, redirect_uris: ${JSON.stringify(client.redirectUris)}`);
        return {
          client_id: client.clientId,
          client_secret: client.clientSecret,
          redirect_uris: client.redirectUris,
          grant_types: client.grantTypes?.length
            ? client.grantTypes
            : ['authorization_code', 'refresh_token'],
          response_types: client.responseTypes?.length ? client.responseTypes : ['code'],
          token_endpoint_auth_method: client.tokenEndpointAuthMethod || 'client_secret_basic',
        };
      }
      this.logger.warn(`OIDC client not found: ${clientId}`);
      return null;
    } catch (error) {
      this.logger.error(`Error finding client ${clientId}:`, error);
      return null;
    }
  }

  async getUserById(id: string) {
    try {
      const user = await this.baseSysUserEntity.findOneBy({ id: parseInt(id) });
      if (!user) return null;

      // 获取用户角色
      const userRoles = await this.baseSysUserRoleEntity.findBy({ userId: user.id });
      const roleIds = userRoles.map(ur => ur.roleId);
      
      // 获取角色信息
      const roles = roleIds.length > 0 ? await this.baseSysRoleEntity.findByIds(roleIds) : [];
      
      // 获取部门信息
      let department = null;
      if (user.departmentId) {
        department = await this.baseSysDepartmentEntity.findOneBy({ id: user.departmentId });
      }

      return {
        ...user,
        roleIdList: roleIds,
        roles: roles,
        departmentName: department?.name,
      };
    } catch (error) {
      this.logger.error(`Error getting user ${id}:`, error);
      return null;
    }
  }

  @Init()
  async initOIDCProvider() {
    try {
      // 使用环境变量配置issuer
      this.issuer = process.env.OIDC_ISSUER || 'http://localhost:1801/luru/oidc';

      this.logger.info(`OIDC Issuer: ${this.issuer}`);

    // 配置Koa应用信任代理
    this.app.proxy = true;

    const configuration: OIDCConfiguration = {
      proxy: true,
      features: {
        devInteractions: { enabled: true },
        revocation: { enabled: true },
        introspection: { enabled: true },
        registration: { enabled: true },
        clientCredentials: { enabled: true },
      },
      responseTypes: ['code'],
      grantTypes: ['authorization_code', 'refresh_token'],
      scopes: ['openid', 'profile', 'email', 'roles'],
      claims: {
        openid: ['sub'],
        profile: ['name', 'nickname', 'preferred_username', 'picture', 'phone_number', 'username', 'department_id', 'department_name', 'status'],
        email: ['email', 'email_verified'],
        roles: ['roles'],
      },
      ttl: {
        Session: () => 24 * 60 * 60, // 24 hours
        AccessToken: () => 60 * 60, // 1 hour
        AuthorizationCode: () => 10 * 60, // 10 minutes
        RefreshToken: () => 30 * 24 * 60 * 60, // 30 days
        IdToken: () => 60 * 60, // 1 hour
        Interaction: () => 60 * 60, // 60 minutes
      },
      interactions: {
        url: async (ctx: KoaContextWithOIDC, interaction) => {
          // 强制使用正确的前端URL路径
          const interactionUrl = `${this.issuer}/interaction/${interaction.uid}`;
          this.logger.info(`Generated interaction URL: ${interactionUrl}`);
          this.logger.info(`Issuer used: ${this.issuer}`);
          return interactionUrl;
        },
      },
      findAccount: async (ctx, id) => {
        try {
          const user = await this.getUserById(id);
          return {
            accountId: id,
            async claims(use, scope) {
              const claims: any = { sub: id };
              if (user) {
                if (scope.includes('profile')) {
                  claims.name = user.name || user.nickName;
                  claims.nickname = user.nickName;
                  claims.preferred_username = user.username;
                  claims.picture = user.headImg;
                  claims.phone_number = user.phone;
                }
                if (scope.includes('email')) {
                  claims.email = user.email;
                  claims.email_verified = !!user.email;
                }
                if (user.roleIdList && user.roleIdList.length > 0) {
                  claims.roles = ['NORMAL'];
                }
                if (user.departmentId) {
                  claims.department_id = user.departmentId;
                }
                if (user.departmentName) {
                  claims.department_name = user.departmentName;
                }
              }
              return claims;
            },
          };
        } catch (error) {
          this.logger.error(`OIDC findAccount error for ${id}:`, error);
          return {
            accountId: id,
            async claims() {
              return { sub: id };
            },
          };
        }
      },
      cookies: {
        keys: this.app.keys,
        names: {
          session: '_oidc_session',
          interaction: '_oidc_interaction',
          resume: '_oidc_resume',
          state: '_oidc_state',
        },
        long: {
          signed: false,
          maxAge: 24 * 60 * 60 * 1000, // 24 hours
          httpOnly: true,
          secure: false, // 禁用安全 cookie，因为代理处理 HTTPS
          path: '/',
        },
        short: {
          signed: false,
          maxAge: 10 * 60 * 1000, // 10 minutes
          httpOnly: true,
          secure: false, // 禁用安全 cookie，因为代理处理 HTTPS
          path: '/',
        },
      },
    } as unknown as OIDCConfiguration;

    // Redis适配器配置
    const storeService = this.store;
    const providerService = this;
    const Adapter: AdapterFactory = (modelName: string) => {
      return {
        name: modelName,
        key(id: string) {
          return `${modelName}:${id}`;
        },
        async upsert(id: string, payload: any, expiresIn?: number) {
          await storeService.upsert(modelName, id, payload, expiresIn);
        },
        async find(id: string) {
          if (modelName === 'Client') {
            return await providerService.findClientById(id);
          }
          return await storeService.find(modelName, id);
        },
        async findByUserCode(userCode: string) {
          return await storeService.findByUserCode(userCode);
        },
        async findByUid(uid: string) {
          return await storeService.findByUid(uid);
        },
        async destroy(id: string) {
          await storeService.destroy(modelName, id);
        },
        async revokeByGrantId(grantId: string) {
          await storeService.revokeByGrantId(grantId);
        },
        async consume(id: string) {
          await storeService.consume(modelName, id);
        },
      };
    };

    const finalConfiguration = {
      ...configuration,
      adapter: Adapter,
    };

    this.provider = new Provider(this.issuer, finalConfiguration);

    // 添加 OIDC Provider 错误处理
    this.provider.on('server_error', (ctx, err) => {
      this.logger.error('OIDC Provider server_error:', err);
      this.logger.error('OIDC Provider error context:', {
        method: ctx.method,
        path: ctx.path,
        query: ctx.query,
        body: ctx.request.body
      });
    });

    this.provider.on('authorization.error', (ctx, err) => {
      this.logger.error('OIDC Provider authorization.error:', err);
    });

    this.provider.on('grant.error', (ctx, err) => {
      this.logger.error('OIDC Provider grant.error:', err);
    });

    // 添加自定义交互页面处理 - 自动登录功能
    this.app.use(async (ctx, next) => {
      // 检查是否是交互页面请求
      if (ctx.path.includes('/oidc/interaction/') && ctx.method === 'GET') {
        this.logger.info(`Handling interaction page: ${ctx.path}`);
        try {
          // 获取交互详情
          const interactionDetails = await this.provider.interactionDetails(ctx.req, ctx.res);
          this.logger.info(`Interaction details: ${JSON.stringify(interactionDetails.prompt)}`);

          // 如果需要登录，返回自定义的登录页面
          if (interactionDetails.prompt.name === 'login') {
            const interactionId = ctx.path.split('/').pop();
            this.logger.info(`Generating auto login page for interaction: ${interactionId}`);

            // 返回自定义的HTML页面，包含自动登录逻辑
            ctx.type = 'text/html';
            ctx.body = this.generateAutoLoginPage(interactionId);
            return;
          }
        } catch (error) {
          this.logger.error('Error handling interaction page:', error);
          this.logger.error('Interaction error stack:', error.stack);
        }
      }

      await next();
    });

    // 添加自动登录API端点
    this.app.use(async (ctx, next) => {
      if (ctx.method === 'POST' && ctx.path.includes('auto-login')) {
        this.logger.info(`Auto login API request: ${ctx.path}`);

        try {
          // 从表单数据获取token和interactionId
          const body = ctx.request.body as any;
          const token = body?.token;
          const interactionId = body?.interactionId;

          this.logger.info(`Auto login attempt - Token: ${token ? token.substring(0, 30) + '...' : 'none'}, InteractionId: ${interactionId}`);

          if (!token || !interactionId) {
            ctx.status = 400;
            ctx.body = { error: 'Missing token or interactionId' };
            return;
          }

          // 验证JWT token
          const decoded = jwt.verify(token, this.jwtConfig.jwt.secret);
          if (!decoded || decoded.isRefresh) {
            ctx.status = 401;
            ctx.body = { error: 'Invalid token' };
            return;
          }

          this.logger.info(`Token verified for user: ${decoded.userId}`);

          // 完成登录交互
          const result = {
            login: {
              accountId: decoded.userId.toString(),
              remember: false,
              ts: Math.floor(Date.now() / 1000),
            },
          };

          // 完成交互 - 这会自动设置重定向响应
          await this.provider.interactionFinished(ctx.req, ctx.res, result, {
            mergeWithLastSubmission: false
          });

          this.logger.info(`Interaction finished successfully`);

        } catch (error) {
          this.logger.error('Auto login error:', error);
          ctx.status = 500;
          ctx.body = { error: 'Auto login failed' };
        }
      } else {
        await next();
      }
    });

    // 添加OIDC请求调试中间件和重定向修复
    this.app.use(async (ctx, next) => {
      if (ctx.path.startsWith('/oidc')) {
        this.logger.info(`OIDC request: ${ctx.method} ${ctx.path}`);
        this.logger.info(`OIDC query params: ${JSON.stringify(ctx.query)}`);
        try {
          await next();
          this.logger.info(`OIDC response status: ${ctx.status}`);

          // 修复重定向URL中的路径问题
          if (ctx.status === 303 && ctx.response.header.location) {
            const originalLocation = ctx.response.header.location;
            this.logger.info(`Original redirect location: ${originalLocation}`);

            // 如果重定向URL缺少 /luru 前缀，则添加它
            if (originalLocation.includes('/oidc/interaction/') && !originalLocation.includes('/luru/oidc/interaction/')) {
              const fixedLocation = originalLocation.replace('/oidc/interaction/', '/luru/oidc/interaction/');
              ctx.response.header.location = fixedLocation;
              this.logger.info(`Fixed redirect location: ${fixedLocation}`);
            }
          }
        } catch (error) {
          this.logger.error(`OIDC request error: ${ctx.method} ${ctx.path}`, error);
          this.logger.error(`Error stack: ${error.stack}`);
          throw error;
        }
      } else {
        await next();
      }
    });

    // 挂载OIDC provider
    this.app.use(mount('/oidc', this.provider));
    this.app.proxy = true;

      this.logger.info('OIDC Provider initialized successfully');
    } catch (error) {
      this.logger.error('OIDC Provider initialization failed:', error);
      throw error;
    }
  }

  /**
   * 生成自动登录页面 - 无界面，直接重定向
   */
  private generateAutoLoginPage(interactionId: string): string {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>授权中...</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body>
    <script>
        (function() {
            try {
                // 从localStorage获取token数据
                let tokenData = localStorage.getItem('token') || localStorage.getItem('access_token');

                if (!tokenData) {
                    window.location.href = '/login?redirect=' + encodeURIComponent(window.location.href);
                    return;
                }

                let token = null;

                // 解析token数据
                try {
                    const parsed = JSON.parse(tokenData);
                    if (typeof parsed === 'string') {
                        token = parsed;
                    } else if (parsed.token) {
                        // 从对象中提取token字段
                        token = parsed.token;
                    } else if (parsed.access_token) {
                        token = parsed.access_token;
                    }
                } catch (e) {
                    // 如果不是JSON格式，直接使用原始值
                    token = tokenData;
                }

                if (!token) {
                    window.location.href = '/login?redirect=' + encodeURIComponent(window.location.href);
                    return;
                }

                // 使用表单提交方式自动登录
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '/oidc/api/auto-login';
                form.style.display = 'none';

                // 添加token到表单
                const tokenInput = document.createElement('input');
                tokenInput.type = 'hidden';
                tokenInput.name = 'token';
                tokenInput.value = token;
                form.appendChild(tokenInput);

                // 添加interactionId到表单
                const idInput = document.createElement('input');
                idInput.type = 'hidden';
                idInput.name = 'interactionId';
                idInput.value = '${interactionId}';
                form.appendChild(idInput);

                // 立即提交表单
                document.body.appendChild(form);
                form.submit();

            } catch (error) {
                console.error('Auto login error:', error);
                // 网络错误，跳转到登录页面
                window.location.href = '/login?redirect=' + encodeURIComponent(window.location.href);
            }
        })();
    </script>
</body>
</html>`;
  }

  /**
   * 重新加载客户端配置
   */
  async reloadClients() {
    try {
      this.logger.info('Reloading OIDC client configurations...');
      const newClients = await this.loadClients();
      this.logger.debug(`Validated ${newClients.length} client configurations`);
      return {
        success: true,
        message: 'Client configurations reloaded successfully. Changes are now active.',
        clientCount: newClients.length,
      };
    } catch (error) {
      this.logger.error('Failed to reload client configurations:', error);
      return {
        success: false,
        message: 'Failed to reload client configurations: ' + error.message,
        clientCount: 0,
      };
    }
  }

  private async loadClients() {
    const clients = [] as any[];
    try {
      const list = await this.clientRepo.findBy({ status: 1 });
      for (const c of list) {
        clients.push({
          client_id: c.clientId,
          client_secret: c.clientSecret,
          redirect_uris: c.redirectUris,
          grant_types: c.grantTypes?.length
            ? c.grantTypes
            : ['authorization_code', 'refresh_token'],
          response_types: c.responseTypes?.length ? c.responseTypes : ['code'],
          token_endpoint_auth_method:
            c.tokenEndpointAuthMethod || 'client_secret_basic',
        });
      }
      if (clients.length) return clients;
    } catch {}

    const env = process.env.OIDC_CLIENTS;
    if (env) {
      try {
        const parsed = JSON.parse(env);
        if (Array.isArray(parsed)) return parsed;
      } catch {}
    }

    clients.push({
      client_id: 'demo-client',
      client_secret: 'demo-secret',
      redirect_uris: ['http://localhost:3000/callback'],
      grant_types: ['authorization_code', 'refresh_token'],
      response_types: ['code'],
      token_endpoint_auth_method: 'client_secret_basic',
    });
    return clients;
  }
}
