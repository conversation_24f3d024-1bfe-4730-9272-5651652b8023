port=
redisHost=
redisPort=
redisPass=
redisDB=
redisTaskDB=
dbHost=
dbUser=
dbPass=
db=
domain=
h5domain=
# OIDC 配置
# OIDC发行者URL，如：http://localhost:8002/luru/oidc 或 https://your-domain.com/luru/oidc
OIDC_ISSUER=http://localhost:8002/luru/oidc

# OIDC Cookie 密钥（生产环境必须更改为强密钥）
OIDC_COOKIE_SECRET=your-secure-cookie-secret-key-here-change-in-production
OIDC_COOKIE_SECRET_OLD=your-old-cookie-secret-for-rotation

# OIDC RSA 密钥对（可选，如果不设置会自动生成并在控制台输出）
# OIDC_RSA_PUBLIC_KEY="-----BEGIN PUBLIC KEY-----\n...\n-----END PUBLIC KEY-----"
# OIDC_RSA_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----"

# OIDC客户端配置（JSON格式），如果数据库中没有配置则使用此环境变量
OIDC_CLIENTS=[{"client_id":"demo-client","client_secret":"demo-secret","redirect_uris":["http://localhost:3000/callback"],"grant_types":["authorization_code","refresh_token"],"response_types":["code"],"token_endpoint_auth_method":"client_secret_basic"}]

# 登录页面URL，用于OIDC认证失败时重定向
LOGIN_URL=/login